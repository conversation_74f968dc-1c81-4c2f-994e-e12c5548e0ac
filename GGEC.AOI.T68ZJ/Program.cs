using GGEC.AOI.T68ZJ.Client;
using GGEC.AOI.T68ZJ.Log;

namespace GGEC.AOI.T68ZJ
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // ��ʼ����־ϵͳ����¼������Ϣ
            Logger.Info("GGEC.AOI.T68ZJ Ӧ�ó�������");

            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            ApplicationConfiguration.Initialize();
            Application.Run(new MainForm());
        }
    }
}